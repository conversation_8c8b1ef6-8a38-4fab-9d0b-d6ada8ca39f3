<template>
  <Teleport to="body">
    <sweet-modal
      ref="assetsAlertModal"
      v-sweet-esc
      class="mb-2"
      :class="getSweetClass()"
      :modal-theme="'right'"
      @close="handleCancel"
    >
      <div class="modal-header d-flex align-items-center px-4 pt-3">
        <h4 class="mt-2">
          {{ name }}
        </h4>
        <p class="sync-text m-0 px-2">
          In Sync
        </p>
      </div>
      <div class="d-flex justify-content-start align-items-center text-left mt-4">
        <div 
          v-for="(alert, index) in integrationAlerts" 
          :key="index"
          :class="alert.class"
          class="alert-card d-flex align-items-center"
          @click="setViewPage(alert.page)"
        >
          <i
            v-tooltip="alert.message.toolTip"
            class="nulodgicon-information-circled mr-3"
            :class="`${alert.class}-icon`"
          />
          <p class="m-0">
            {{ alert.message.start }}<strong class="strong-text">{{ name }}</strong>{{ alert.message.end }}
          </p>
        </div>
      </div>

      <div class="d-flex flex-column justify-content-start mx-4 mt-3">
        <div class="d-flex align-items-center mb-3">
          <h4 class="content-heading m-0 mr-3 pt-1">
            {{ name }} Assets Overview
          </h4>
          <div class="subpage-menu mt-1">
            <div
              v-for="page in pages"
              :key="page.name"
              v-tooltip="page.tooltip"
              :class="['clickable subpage-menu__item', isActiveClass(page)]"
              @click="setViewPage(page)"
            >
              {{ page.label }}
            </div>
          </div>
        </div>
        <div class="d-flex justify-content-between">
          <div class="col-md-6 p-0">
            <search-input-logs />
          </div>
          <div class="d-flex mt-1">
            <results-per-page-logs />
            <div
              v-if="pageCountLogs > 1"
              class="float-right ml-3 mt-1"
            >
              <paginate
                ref="paginate"
                :click-handler="pageSelected"
                :container-class="'pagination pagination-sm'"
                :next-class="'next-item'"
                :next-link-class="'page-link'"
                :next-text="'Next'"
                :page-class="'page-item'"
                :page-count="pageCountLogs"
                :page-link-class="'page-link'"
                :prev-class="'prev-item'"
                :prev-link-class="'page-link'"
                :prev-text="'Prev'"
                :selected="page"
                class="float-right mb-0"
              />
            </div>
          </div>
        </div>
        <div 
          ref="table"
          class="d-flex row mt-3 justify-content-center align-items-center"
        >
          <span 
            v-if ="loadingLogs"
            class="d-flex justify-content-center mt-5"
          >
            <h5 class="text-muted float-left">
              Loading assets
            </h5>
            <sync-loader
              :loading="loadingLogs"
              class="ml-3 mt-1 d-inline"
              color="#0d6efd"
              size="0.3rem"
            />
          </span>
          <div
            v-else-if="areAssetsEmpty()"
            class="text-center mt-5 clear-both"
          >
            <h4>There are no discovered assets here.</h4>
            <h5
              v-if="searchLogs"
              class="text-secondary font-weight-normal"
            >
              Please revise your search.
            </h5>
          </div>
          <div 
            v-else 
            class="w-100 px-3"
          >
            <asset-log-list 
              :name="name"
              @resync="resyncIntegration"
            />
            <div
              v-if="pageCountLogs > 1"
              class="float-right ml-3 mt-1"
            >
              <paginate
                ref="paginate"
                :click-handler="pageSelected"
                :container-class="'pagination pagination-sm'"
                :next-class="'next-item'"
                :next-link-class="'page-link'"
                :next-text="'Next'"
                :page-class="'page-item'"
                :page-count="pageCountLogs"
                :page-link-class="'page-link'"
                :prev-class="'prev-item'"
                :prev-link-class="'page-link'"
                :prev-text="'Prev'"
                :selected="page"
                class="float-right mb-0 pt-3"
              />
            </div>
          </div>
        </div>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import { mapMutations, mapGetters } from 'vuex';
  import Paginate from 'vuejs-paginate';
  import permissionsHelper from 'mixins/permissions_helper';
  import pageSizeHelper from 'mixins/page_size_helper';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import searchInputLogs from './search_input_logs.vue';
  import ResultsPerPageLogs from './results_per_page_logs.vue';
  import AssetLogList from './discovered_assets/asset_log_list.vue';

  export default {
    components: {
      SweetModal,
      AssetLogList,
      searchInputLogs,
      SyncLoader,
      Paginate,
      ResultsPerPageLogs,
    },
    mixins: [permissionsHelper, pageSizeHelper],
    props: {
      openModal: {
        type: Boolean,
        default: false,
      },
      alertsInfo: {
        type: Object,
        default: null,
      },
      name: {
        type: String,
        default: 'Integration',
      },
    },
    data() {
      return {
        pages: [
          { name: 'all', label: 'All Assets', hasSeen: true },
          { name: 'not_reporting_missing', label: 'Missing / Not Reporting Assets', hasSeen: false, tooltip: "Assets that are missing or have not reported for more than 7 days" },
          { name: 'new', label: 'New Assets', hasSeen: false, tooltip: "Newly reported assets, not yet in Genuity" },
        ],
        viewPage: { name: 'all', label: 'All Assets' },
      };
    },
    computed: {
      ...mapGetters([
        'assetSources',
        'discoveredAssetSource',
        'discoveredAssetLocation',
        'discoveredAssetStatus',
        'discoveredAssetsLogs',
        'assetDiscovery',
        'loadingLogs',
        'searchLogs',
        'pageSizeLogs',
        'pageCountLogs',
        'pageIndexLogs',
      ]),
      getStatus() {
        if (this.viewPage.name === 'old_assets') {
          return 'ready_for_import';
        }
        return 'imported';
      },
      page() {
        return this.pageIndexLogs;
      },
      status() {
        return this.discoveredAssetStatus;
      },
      assetSourceId() {
        if (this.discoveredAssetSource) {
          return this.discoveredAssetSource.id;
        }

        return null;
      },
      currentLocationId() {
        if (this.discoveredAssetLocation) {
          return this.discoveredAssetLocation.id;
        }

        return null;
      },
      integrationAlerts() {
        if (!this.alertsInfo) return null;

        const { notReporting, checkAssets, newAssets } = this.alertsInfo;
        const pluralize = (count, singular, plural) => count === 1 ? singular : plural;

        const notReportingWords = {
          isAre: pluralize(notReporting + checkAssets, 'is', 'are'),
          assets: pluralize(notReporting + checkAssets, 'asset', 'assets'),
        };

        const newWords = {
          isAre: pluralize(newAssets, 'is', 'are'),
          assets: pluralize(newAssets, 'asset', 'assets'),
          isNAre: pluralize(newAssets, "isn't", "aren't"),
        };

        const notReportingMessage = {
          start: `We found ${notReporting + checkAssets} active ${notReportingWords.assets} in Genuity that ${notReportingWords.isAre} missing or ${notReportingWords.isAre}  no longer being reported by `,
          end: '.',
          toolTip: `${notReporting + checkAssets} ${notReportingWords.assets} ${notReportingWords.isAre} not reporting or ${notReportingWords.isAre} missing`,
        };

        const newAssetsMessage = {
          start: '',
          end: ` has reported ${newAssets} ${newWords.assets} that ${newWords.isNAre} yet stored in Genuity.`,
          toolTip: `${newAssets} ${newWords.assets} ${newWords.isAre} new`,
        };

        return [
          { message: notReportingMessage, count: notReporting + checkAssets, class: "not-reporting", page: this.pages[1] }, 
          { message: newAssetsMessage, count: newAssets, class: "new-assets", page: this.pages[2] }, 
        ].filter(alert => alert.count > 0);
      },
    },
    mounted() {
      this.setDiscoveredAssetStatus(null);
      this.setAssetDiscovery("all");
      this.$store.dispatch('fetchDiscoveredAssetsLogs');
      this.updateDiscoveredAssets();
      if (this.openModal) {
        this.$refs.assetsAlertModal.open();
      }
    },
    destroyed() {
      this.setDiscoveredAssetStatus(this.$route.name);
      this.setAssetDiscovery(null);
    },
    methods: {
      ...mapMutations([
        'setDiscoveredAssetStatus',
        'setPageIndex',
        'setDiscoveredAssets',
        'setDiscoveredAssetSource',
        'setDiscoveredAssetLocation',
        'setPageSize',
        'setAssetDiscovery',
        'setPageIndexLogs',
        'setPageSizeLogs',
      ]),
      isActiveClass(page) {
        return { 'active-tab': this.viewPage.name === page.name };
      },
      setViewPage(page) {
        this.viewPage = page;
        const foundPage = this.pages.find((p) => p.name === page.name);
        if (foundPage) foundPage.hasSeen = true;

        this.setPageIndexLogs(0);
        this.setPageSizeLogs(25);

        this.setAssetDiscovery(foundPage.name);
        this.$store.dispatch('fetchDiscoveredAssetsLogs');
      },
      handleCancel() {
        this.$emit('close-modal');
      },
      updateDiscoveredAssets() {
        if (this.discoveredAssetStatus !== this.status) {
          this.checkPerPage("discovered-assets", this.setPageSize);
          this.setDiscoveredAssetStatus(this.status);
          this.$store.dispatch('fetchDiscoveredAssetsLogs');
        }
      },
      pageSelected(p) {
        this.setPageIndexLogs(p - 1);
        this.$store.dispatch("fetchDiscoveredAssetsLogs");
      },
      getSweetClass() {
        return this.loadingLogs || this.discoveredAssetsLogs.length <= 0 ? "loading" : "loaded";
      },
      areAssetsEmpty() {
        return this.discoveredAssetsLogs.length <= 0;
      },
      resyncIntegration() {
        this.$emit('resync');
      },
    },
  };
</script>

<style scoped>
  .active-tab {
    background-color: #fde5e5;
    color:#9b3634
  }

  hr {
    border: 0;
    margin-top: 0.1rem;
    border-top: 0.1rem solid #DEE2E6;
  }

  .show-text {
    margin-top: 0.5rem;
    font-size: medium;
    margin-left: 0.25rem;
    margin-bottom: 0.5rem;
  }

  ::v-deep .sweet-modal .sweet-content .sweet-content-content {
    margin-top: -1rem !important;
  }

  ::v-deep(.sweet-modal.theme-right) {
    min-width: 90%;
    overflow-x: hidden;
  }

  .loaded ::v-deep(.sweet-modal.theme-wide) {
    max-height: 76%;
  }

  .loading ::v-deep(.sweet-modal.theme-wide) {
    height: 40%;
  }

  .modal-header {
    background-color: var(--themed-dark-drawer-bg);
    color: white;
    display: flex;
    gap: 1rem;
    align-items: center;
    width: 110%;
    height: 5rem;
    margin-left: -2rem;
    margin-top: -5rem;
  }

  .sync-text {
    background: #4e8b5f;
    border-radius: 1rem;
  }

  ::v-deep(.sweet-action-close) {
    color: #ffffff !important;
  }

  .content-heading {
    font-weight: 100;
  }

  .subpage-menu {
    max-width: fit-content !important;
  }

  .alert-card {
    cursor: pointer;
    padding: 1rem 1.75rem;
    margin: 1rem;
    background: var(--themed-orange-subtle);
    color: #ba651d;
    border-radius: 0.75rem;
    box-shadow: 0px 8px 5px -4px #d2b49b;
    transition: transform 0.2s ease; 
  }

  .alert-card:hover {
    transform: translateY(-2px);  
  }


  .strong-text {
    border-bottom: 0.15rem solid;
  }

  .not-reporting {
    background: var(--themed-not-reporting-row);
    color:  var(--themed-asset-not-reporting);
    box-shadow: 0px 8px 5px -4px var(--themed-not-shadow);
  }

  .check-assets {
    background: var(--themed-check-row);
    color: var(--themed-asset-check);
    box-shadow: 0px 8px 5px -4px var(--themed-check-shadow);
  }

  .new-assets {
    background: var(--themed-new-row);
    color: var(--themed-blue-dark);
    box-shadow: 0px 8px 5px -4px var(--themed-new-shadow);
  }

  .not-reporting-icon {
    color: var(--themed-asset-not-reporting);
    font-size: 1.25rem;
  }

  .check-assets-icon {
    color: var(--themed-asset-check);
    font-size: 1.25rem;
  }

  .new-assets-icon {
    color: var(--themed-blue-dark);
    font-size: 1.25rem;
  }
</style>
