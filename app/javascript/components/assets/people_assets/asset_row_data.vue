<template>
  <div> 
    <div 
      v-if="preference.name == 'device'"
      class="row-font-size d-flex align-items-center"
    >
      <div class="device-icon">
        <icon-badge
          :tooltip-message="asset.assetType"
          :img-src="imageSrc"
          :img-height-pixels="32"
          :img-padding-pixels="6"
          :background-color-class="`bg-themed-icon-badge-bg`"
        />
      </div>
      <div class="device-details ml-2">
        <span
          v-tooltip="{
            content: asset.name,
            show: showTooltip,
            trigger: 'manual'
          }"
          class="device-name"
          @mouseover="hoveredIndex = asset.id"
          @mouseleave="hoveredIndex = null"
        >
          {{ truncatedName }}
        </span>
        <div class="device-model text-muted">
          {{ truncatedModel }}
        </div>
      </div>
    </div>
   
    <div 
      v-else-if="preference.name == 'status'" 
      @click.stop.prevent="toggleStatusDropdown"
    >
    
      <asset-status-pill
        :show-arrow="true"
        :name="assetStatus.name"
        :icon="assetStatus.icon"
      />
      <basic-dropdown
        v-if="showStatusDropdown"
        ref="dropdown"
        class="dropdown-menu w-100 not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
        dropdown-height="auto"
        dynamic-content
        is-asset-module
        :show-dropdown="showStatusDropdown"
        @on-blur="closeStatusDropdown"
      >
        <a
          v-for="status in statusOptions"
          :key="status.id"
          href="#"
          class="dropdown-item d-flex align-items-center text-truncate"
          @click.prevent="changeStatus(status)"
        >
          <div class="d-flex align-items-center w-100">
            <asset-status-icon
              :icon="status.icon"
              :name="status.name"
              class="mr-2"
            />
            <span
              v-tooltip="toTitle(status.name).length > 16 ? toTitle(status.name) : false"
              :class="getTextColor(status.icon)"
              class="text-truncate d-inline-block status-text-limit"
            >{{ toTitle(status.name) }}
            </span>
          </div>
        </a>
      </basic-dropdown>
    </div>

    <span
      v-else-if="preference.name === 'used_by_contributor_id'"
      class="mt-1 mb-0"
      @click.stop
    >
      <span
        class="mb-0 medium text nowrap d-flex align-items-center justify-content-evenly"
        @click.prevent="toggleDropdown"
      >
        <user-avatar
          v-if="objName(computedUsedByContributor)"
          class="position-relative clearfix logo-outline"
          :username="objName(computedUsedByContributor)"
          :src="userAvatar(computedUsedByContributor)"
          :size="32"
          inline
        />
        <div
          v-else
          v-tooltip="`Not assigned yet`"
          class="mb-0 medium text"
        >
          <user-avatar
            class="d-inline-block nulodgicon-person avatar--created-by--empty small"
            :size="32"
            :username="``"
          />
        </div>
        <span class="ml-2">
          {{ objName(computedUsedByContributor) || 'Not Assigned' }}
        </span>
        <span class="ml-2">
          <i :class="'dropdown-toggle'" />
        </span>
      </span>
      <basic-dropdown
        ref="dropdown"
        class="dropdown-menu not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
        dropdown-height="22rem"
        dynamic-content
        :show-dropdown="showDropdown"
        @on-blur="closeDropdown"
      >
        <contributors-select
          v-if="showDropdown"
          ref="contributorSelect"
          class="users-select"
          placeholder="Select people"
          compact
          :value="computedUsedByContributor"
          @list-open="listOpen"
          @select="onUsedByContributorChange"
          @remove="onUsedByContributorRemove"
        />
      </basic-dropdown>
    </span>

    <span
      v-else-if="preference.name == 'location'"
      class="mt-1 mb-0"
      @click.stop
    >
      <a
        href="#"
        class="mb-0 text-black nowrap d-flex align-items-center justify-content-evenly"
        @click.prevent="toggleLocationDropdown"
      >
        <span class="ml-2">
          {{ locationName(asset)|| 'No Location' }}
        </span>
        <span class="ml-2">
          <i :class="'dropdown-toggle'" />
        </span>
      </a>
      <basic-dropdown
        v-if="showLocationsDropdown"
        ref="locationsDropdown"
        class="dropdown-menu not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
        dropdown-height="5rem"
        dynamic-content
        :show-dropdown="showLocationsDropdown"
        @on-blur="closeLocationsDropdown"
      >
        <location-options
          v-if="showLocationsDropdown"
          ref="locationsList"
          :multiple="false"
          :asset-location-id="asset.locationId"
          :managed-asset="asset"
          @input="assignLocation"
        />
      </basic-dropdown>
     
    </span>

    <span
      v-else-if="preference.name === 'managed_by_contributor_id'"
      class="mt-1 mb-0"
      @click.stop
    >
      <span
        class="mb-0 medium text nowrap d-flex align-items-center justify-content-evenly"
        @click.prevent="toggleDropdown"
      >
        <user-avatar
          v-if="objName(computedManagedByContributor)"
          class="position-relative clearfix logo-outline"
          :username="objName(computedManagedByContributor)"
          :src="userAvatar(computedManagedByContributor)"
          :size="32"
          inline
        />
        <div
          v-else
          v-tooltip="`Not assigned yet`"
          class="mb-0 medium text"
        >
          <user-avatar
            class="d-inline-block nulodgicon-person avatar--created-by--empty small"
            :size="32"
            :username="``"
          />
        </div>
        <span class="ml-2">
          {{ objName(computedManagedByContributor) || 'Not Assigned' }}
        </span>
        <span class="ml-2">
          <i :class="'dropdown-toggle'" />
        </span>
      </span>
      <basic-dropdown
        ref="dropdown"
        class="dropdown-menu not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
        dropdown-height="22rem"
        dynamic-content
        :show-dropdown="showDropdown"
        @on-blur="closeDropdown"
      >
        <contributors-select
          v-if="showDropdown"
          ref="contributorSelect"
          class="users-select"
          name="managed_asset[assignment_information_attributes][managed_by_contributor_id]"
          placeholder="Select people"
          compact
          :value="computedManagedByContributor"
          @list-open="listOpen"
          @select="onManagedByContributorChange"
          @remove="onManagedByContributorRemove"
        />
      </basic-dropdown>
    </span>
    
    <span
      v-else-if="preference.name == 'tags'"
      class="d-flex w-100 flex-wrap"
    >
      <tag
        v-for="tag in currentAssetTags"
        :key="tag.id"
        class="px-2 mb-1 mr-0 mr-1 font-weight-semi-bold tag-width"
      >
        {{ tag.name }}
      </tag>

      <a
        v-if="!isMergedAsset"
        href="#"
        class="text-secondary small mb-1 d-inline-block align-bottom mx-1"
        @click.stop.prevent="toggleTagsDropdown"
      >
        + Add tag
      </a>
     
      <basic-dropdown
        ref="dropdown"
        class="dropdown-menu not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
        dropdown-height="22rem"
        dynamic-content
        :show-dropdown="showTagsDropdown"
        @on-blur="closeTagsDropdown"
      >
        <div class="form-group">
          <multi-select
            v-if="showTagsDropdown"
            ref="tagsDropdown"
            placeholder="Search for or type in a new tag"
            tag-placeholder="Add a tag"
            :multiple="true"
            :options="tagOptions"
            :taggable="true"
            :value="tagOptionsFromAsset"
            @tag="addNewTagSelection"
            @remove="removeTagSelection"
            @select="addNewTagSelection"
          />
        </div>
      </basic-dropdown>
    </span>

    <span
      v-else-if="preference.name == 'source'"
      class="mt-1 small text-muted mb-0"
    >
      <span v-if="asset.sources">
        <span
          v-for="source in asset.sources"
          :key="source.id"
        >
          <icon-badge
            class="d-inline-block mr-1 my-1"
            :tooltip-message="sourceName(source)"
            :img-src="getSourceIcon(source)"
            :img-height-pixels="24"
            :img-padding-pixels="3"
            :background-color-class="`bg-themed-icon-badge-bg`"
          />
        </span>
      </span>
      <span v-else>
        <icon-badge
          class="d-inline-block mr-1 my-1"
          :tooltip-message="asset.source"
          :img-src="getSourceIcon(asset.source)"
          :img-height-pixels="24"
          :img-padding-pixels="3"
          :background-color-class="`bg-themed-icon-badge-bg`"
        />
      </span>
    </span>

    <div v-else-if="preference.name === 'last_check_in'">
      {{ formattedLastCheckIn }}
    </div>
  </div> 
  
</template>

<script>
  import http from 'common/http';
  import { Avatar as UserAvatar } from 'vue-avatar';
  import { mapActions, mapGetters, mapMutations } from 'vuex';
  import dates from 'mixins/dates';
  import strings from 'mixins/string';
  import assetImages from 'mixins/asset_images';
  import assetHelper from 'mixins/assets/asset_helper';
  import dropdownClose from 'mixins/dropdown_close_outside_click';
  import assetStatus from 'mixins/assets/asset_status';
  import MultiSelect from 'vue-multiselect';
  import Tag from "../../shared/tag.vue";
  import IconBadge from '../../shared/icon_badge.vue';
  import AssetStatusPill from '../../shared/asset_status_pill.vue';
  import ContributorsSelect from '../../shared/contributors_select.vue';
  import BasicDropdown from '../../shared/basic_dropdown.vue';
  import AssetStatusIcon from '../../shared/asset_status_icon.vue';
  import LocationOptions from '../../shared/location_options.vue';

  export default {
    name: "PeopleAssetRowData",
    components: {
      Tag,
      IconBadge,
      MultiSelect,
      AssetStatusPill,
      LocationOptions,
      AssetStatusIcon,
      ContributorsSelect,
      BasicDropdown,
      UserAvatar,
    },
    mixins: [dates, strings, assetImages, assetHelper, dropdownClose, assetStatus],
    props: {
      asset: {
        type: Object,
        required: true,
      },
      preference: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        truncatedModel: '',
        truncatedName: '',
        hoveredIndex: null,
        usedByContributor: null,
        usedByContributorId: null,
        managedByContributor: null,
        managedByContributorId: null,
        showDropdown: false,
        statusOptions: [],
        showLocationsDropdown: false,
        showTagsDropdown: false,
        preferencesWithTooltip: [
          'model',
          'asset_tag',
        ],
        showStatusDropdown: false,
        offset: 0,
        pageSize: 100,
      };
    },
    computed: {
      ...mapGetters(['assetAvailabilityStatus', 'assetNameColumnWidth', 'assetTags']),
      ...mapGetters('GlobalStore', ['locations']),

      showTooltip() {
        return (
          this.asset.name &&
          this.truncatedName !== this.asset.name &&
          this.hoveredIndex === this.asset.id
        );
      },
      computedUsedByContributor() {
        return this.usedByContributor ?? this.asset.usedBy;
      },
      computedManagedByContributor() {
        return this.managedByContributor ?? this.asset.managedBy;
      },
      formattedLastCheckIn() {
        if (!this.asset.lastCheckIn)
          return '';

        return this.showDateTime(this.asset.lastCheckIn);
      },
      assetSources() {
        return this.asset.sources.map(source => this.toTitle(source)).join(", ");
      },
      imageSrc() {
        return this.asset.imageThumbUrl
          ? this.asset.imageThumbUrl
          : this.assetTypeImageSrc(this.asset);
      },
      assetStatus() {
        const status = this.assetAvailabilityStatus.find(s => s.name === this.asset.status);
        return status || {};
      },
      isMergedAsset() {
        return this.asset.merged;
      },
      currentStatus() {
        return this.asset.status;
      },
      tagOptionsFromAsset() {
        return this.asset?.tags?.map((tag) => tag.name) || [];
      },
      tagOptions() {
        return this.assetTags?.map((tag) => (tag.name)) || [];
      },
      currentAssetTags() {
        return this.asset?.tags || [];
      }, 
    },
    mounted() {
      this.updateTruncatedName();
    },
    methods: {
      ...mapMutations(['setCurrentAsset', 'updateAsset', 'setPreventTableLoading']),
      ...mapActions(['fetchAssets']),
      ...mapActions('GlobalStore', ['fetchLocations']),
      async toggleDropdown() {
        this.showDropdown = !this.showDropdown;
        if (this.showDropdown) {
          await this.$nextTick();
          if (this.$refs.contributorSelect?.$refs?.multiselect) {
            this.$refs.contributorSelect.$refs.multiselect.activate();
          }
        }
      },
      toggleTagsDropdown() {
        this.showTagsDropdown = !this.showTagsDropdown;
        if (this.showTagsDropdown) {
          this.$nextTick(() => {
            if (this.$refs.tagsDropdown) {
              this.$refs.tagsDropdown.activate();
              this.tagsDropdownActivated = true;
            }
          });
        }
      },
      toggleLocationDropdown() {
        this.showLocationsDropdown = !this.showLocationsDropdown;
        if (this.showLocationsDropdown) {
          this.fetchLocations();
          this.$nextTick(() => {
             if (this.$refs.locationsList) {
            this.$refs.locationsList.$refs?.multiselect?.activate();
          }
          });     
        }
      },
      closeDropdown() {
        this.showDropdown = false;
      },
      listOpen(flag) {
        if(!flag) {
          this.closeDropdown();
        }
      },
      closeTagsDropdown() {
        this.showTagsDropdown = false;
      },
      listTagsOpen(flag) {
        if(!flag) {
          this.closeTagsDropdown();
        }
      },
      closeLocationsDropdown() {
        this.showLocationsDropdown = false;
      },
      listLocationsOpen(flag) {
        if(!flag) {
          this.closeLocationsDropwdown();
        }
      },
      toggleStatusDropdown() {
        if (!this.isMergedAsset) {
          this.showStatusDropdown = !this.showStatusDropdown;
          if (this.showStatusDropdown) {
            this.getStatusOptions();
          }
        } else {
          this.emitError(`Sorry, you do not have permissions to set status.`);
        }
      },
      closeStatusDropdown() {
        this.showStatusDropdown = false;
      },
      objName(obj) {
        if (obj) {
          return obj.name || obj.fullName || obj.email || obj.$groupLabel;
        }
        return null;
      },
      userAvatar(option) {
        return option.avatar || option.avatarThumbUrl;
      },
      updateTruncatedName() {
        const maxChars = Math.floor(250 / 7);
        this.truncatedName = this.asset?.name?.length > maxChars
          ? `${this.asset.name.slice(0, maxChars - 3)}...`
          : this.asset.name;

        this.truncatedModel = this.asset?.model?.length > maxChars
          ? `${this.asset.model.slice(0, maxChars - 3)}...`
          : this.asset.model;
      },
      sourceName(source) {
        if (source === 'selfonboarding') {
          return 'Self Onboarding';
        }
        return this.toTitle(source);
      },
      locationName(asset) {
        if(asset.locationName) {
          return asset.locationName;
        }
        return this.locations.find((loc) => loc.id === asset.locationId)?.name;
      },
      onUsedByContributorChange(obj) {
        this.handleContributorChange({ key: 'usedByContributor', value: obj, isAssign: true });
      },
      onUsedByContributorRemove(obj) {
        this.handleContributorChange({ key: 'usedByContributor', value: obj, isAssign: false });
      },
      onManagedByContributorChange(obj) {
        this.handleContributorChange({ key: 'managedByContributor', value: obj, isAssign: true });
      },
      onManagedByContributorRemove(obj) {
        this.handleContributorChange({ key: 'managedByContributor', value: obj, isAssign: false });
      },
      handleContributorChange({ key, value, isAssign }) {
        this[`${key}Id`] = value?.id;
        this[key] = isAssign ? value : null;
        this.closeDropdown();
        isAssign ? this.assignUsersSubmit() : this.removeUsersSubmit();
      },
      assignUsersSubmit() {
        if (this.usedByContributorId || this.managedByContributorId) {
          http
            .post(`/bulk_managed_assets/inline_assign_users.json`, {
              assetIds: this.asset.id,
              managedByContributorId: this.managedByContributorId,
              usedByContributorId: this.usedByContributorId,
            })
            .then(() => {
              this.setPreventTableLoading(true);
              this.fetchAssets();
            })
            .catch((err) => {
              this.emitError(
                `Sorry, there was an error assigning users to these assets: ${err.response ? err.response.data.message : err.message}`
              );
            });
        }
      },
      removeUsersSubmit() {
        if (this.usedByContributorId || this.managedByContributorId) {
          http
            .post(`/bulk_managed_assets/inline_remove_users.json`, {
              assetIds: this.asset.id,
              managedByContributorId: this.managedByContributorId,
              usedByContributorId: this.usedByContributorId,
            })
            .then(() => {
              this.setPreventTableLoading(true);
              this.fetchAssets();
            })
            .catch((err) => {
              this.emitError(
                `Sorry, there was an error removing users to these assets: ${err.response ? err.response.data.message : err.message}`
              );
            });
        }
      },
      onBlur() {
        if (!document.activeElement?.classList.contains("multiselect__input")) {
          this.showDropdown = false;
        }
      },
      getStatusOptions() {
        http
          .get('/managed_asset_statuses.json')
          .then(res => {
            this.statusOptions = res.data;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error getting asset status options. Please refresh the page and try again`);
          });
      },
      changeStatus(status) {
        http
          .put(`/managed_asset_statuses/${this.asset.id}.json?status_id=${status.id}`)
          .then(() => {
            this.$set(this.asset, 'status', status.name);
            this.closeStatusDropdown();
            this.emitSuccess(`Successfully updated the asset status`);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error updating this asset status ${error.response.data.message}`);
          });
      },
      addNewTagSelection(newTag) {
        const tag = this.assetTags.find((t) => t.name === newTag);
        const params = { asset_id: this.asset.id };
        if (tag) {
          params.company_tag_id = tag.id;
        } else {
          params.new_tag = newTag.toLowerCase();
        }

        http
          .post(`/managed_asset_tags.json`, params)
          .then((res) => {
              const newTagObj = params.new_tag
                ? { id: res.data.assetTag.id, name: newTag }
                : { id: tag.id, name: tag.name };

              const updatedTags = [...(this.asset.tags || []), newTagObj];
              this.$set(this.asset, 'tags', updatedTags);
              this.closeTagsDropdown();
              this.emitSuccess("Tag added successfully.");
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error adding this tag (${error.response.data.message}).`);
          });
      },
      removeTagSelection(tagName) {
        const tag = this.asset.tags.find((t) => t.name === tagName);

        http
          .delete(`/managed_asset_tags/${tag.id}.json`)
          .then(() => {
            const updatedTags = this.asset.tags.filter((t) => t.id !== tag.id);
            this.$set(this.asset, 'tags', updatedTags);

            this.closeTagsDropdown();
            this.emitSuccess("Tag removed successfully.");
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error removing this tag (${error.response.data.message}).`);
          });
      },
      assignLocation(location) {
        const locId = !location ? null : location.id;
        const params = {
          managed_asset: {
            location_id: locId,
          },
        };

        http
          .put(`/managed_assets/${this.asset.id}.json`, params)
          .then((res) => {
            if (res.data?.asset && res.data.asset.location) {
              this.$set(this.asset, 'locationName', res.data.asset.location.name);
              this.$set(this.asset, 'locationId', res.data.asset.location.id);
              this.closeLocationsDropdown();
            } else {
              this.$set(this.asset, 'locationName', null);
              this.$set(this.asset, 'locationId', null);
              this.closeLocationsDropdown();
            }   
            this.emitSuccess("Location updated successfully.");
          })
          .catch(error => {
            this.closeLocationsDropdown();
            this.emitError(`Error updating location: ${error.response?.data?.message}`);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .row-font-size {
    font-size: 0.875rem;
  }

  .device-icon {
    display: flex;
    align-items: center;
  }

  .device-details {
    display: flex;
    flex-direction: column;
  }

  .device-name {
    max-width: auto;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }

  .device-model {
    font-size: 0.75rem;
    color: #6c757d;
  }

  .tag-width {
    display: block;
    width: fit-content;
  }

  .status-text-limit {
    max-width: 160px;
  }
</style>
