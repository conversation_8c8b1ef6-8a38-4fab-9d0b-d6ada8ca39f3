<template>
  <div
    v-if="showBanner"
    class="box py-2 my-4 template-banner"
  >
    <div class="box__inner">
      <div class="row">
        <div class="col-5">
          <h6 class="mb-0 my-3">
            Automatically handle your routine tasks
          </h6>

          <ul class="mb-0 text-themed-fair mb-3">
            <li class="not-as-small mt-2 mb-1">
              <span class="nulodgicon-checkmark small" /> {{ moduleActions }}
            </li>
            <li class="not-as-small mb-1">
              <span class="nulodgicon-checkmark small" /> {{ isAssetsModule ? 'Create a new task' : 'Create a new task or clone existing ones below.' }}
            <li class="not-as-small">
              <span class="nulodgicon-checkmark small" /> Drag and drop tasks to customize execution order.
            </li>
          </ul>
        </div>
        <div class="col-7">
          <img
            :src="imageSrc"
            width="100%"
            height="150"
          >
        </div>
      </div>
      <a
        class="banner__close-button d-inline-flex basic-transition group-header__arrow-wrap align-items-center p-0 align-middle text-center justify-content-center rounded-circle text-very-muted bg-light"
        @click="closeBanner"
      >
        <i
          v-tooltip="'Dismiss'"
          class="nulodgicon-android-close text-very-muted"
        />
      </a>
    </div>
  </div>
</template>

<script>
  import { mapMutations, mapGetters } from 'vuex';

  export default{
    data() {
      return {
        showBanner: true,
      };
    },
    computed: {
      ...mapGetters(['currentModule']),
      isHelpDesk() {
        return this.currentModule === 'helpdesk';
      },
      moduleActions() {
        if (this.isHelpDesk) {
          return 'Flexibly assign users, prioritize tickets, and more.';
        }
        return `Flexibly assign users, alert on hardware status, and more.`;
      },
      storageKey() {
        return this.isHelpDesk ? `${this.currentModule}_${$workspace.id}_show_banner` : `managed_assets_show_banner`;
      },
      imageSrc() {
      return this.isHelpDesk
        ? "https://nulodgic-static-assets.s3.amazonaws.com/images/automated_task_visualization.gif"
        : "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/assets_automated_tasks_visualization.gif";
      },
      isAssetsModule() {
        return window.location.href.includes('managed_assets');
      },
    },
    watch: {
      currentModule() {
        this.showBanner = localStorage.getItem(this.storageKey) || true;
      },
    },
    methods: {
      ...mapMutations(['setCurrentModule']),
      closeBanner() {
        this.showBanner = false;
        localStorage.setItem(this.storageKey, this.showBanner);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .template-banner {
    color: white;
    background: rgb(27,55,94);
    background: linear-gradient(124deg, rgba(4,30,66,1) 38%, rgba(4,30,66,1) 38%, rgba(255,255,255,1) 38%);
  }

  .banner__close-button {
    height: 1.75rem;
    position: absolute;
    right: -10px;
    top: -10px;
    width: 1.75rem;

    .nulodgicon-android-close:before {
      color: $themed-secondary !important;
    }
  }
</style>
