<template>
  <sweet-modal
    v-if="value"
    ref="modal"
    v-sweet-esc
    title="Before you delete this group"
  >
    <template>
      <div
        data-tc-title-before-delete-modal
      >
        <h6>
          Are you absolutely sure you want to delete this group?
        </h6>
        <p class="text-secondary mt-3">
          <i>
            <strong>Note</strong>: This may result in users losing permissions and/or losing data.
          </i>
        </p>
      </div>
      <div v-if="dependentTasks.length >= 1">
        <h5 class="text-danger">
          Note:
        </h5>
        <p>
          Looks like the group is being used in a few automated tasks. If you continue, the related automated tasks will be altered/deleted.
        </p>
      </div>
    </template>
    <button
      slot="button"
      class="btn btn-text mr-2"
      data-tc-cancel-delete-group-btn
      @click.prevent="close"
    >
      No, keep it
    </button>
    <button
      slot="button"
      :disabled="isLoading"
      class="btn btn-danger"
      data-tc-ok-delete-group-btn
      @click.prevent="okDelete"
    >
      Yes, I'm sure
    </button>
  </sweet-modal>
</template>

<script>
import http from 'common/http';
import { SweetModal } from 'sweet-modal-vue';

export default {
  components: {
    SweetModal,
  },
  props: ['value'],
  data() {
    return {
      isLoading: false,
      dependentTasks: [],
    };
  },
  methods: {
    close() {
      this.$refs.modal.close();
    },
    open() {
      this.checkGroupDependencies();
      this.$refs.modal.open();
    },
    checkGroupDependencies() {
      this.isLoading = true;
      http
        .get(`/company/groups/${this.value.id}/group_dependent_automated_task.json`)
        .then(res => {
          this.dependentTasks = res.data.dependentTasks;
          this.isLoading = false;
        });
      },
    okDelete() {
      http
        .delete(`/groups/${this.value.id}.json`)
        .then(() => {
          this.emitSuccess('Group was successfully removed.');
          this.$emit('group-deleted');
          this.close();
        })
        .catch(error => {
          this.emitError(`Sorry, there was an issue deleting this group (${error.response.data.message}).`);
          this.close();
        });
    },
  },
};
</script>
