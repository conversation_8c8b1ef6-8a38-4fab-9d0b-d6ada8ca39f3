<template>
  <div
    :key="$route.path"
    :class="ticketShowStyles"
  >
    <div>
      <sweet-modal
        ref="disableTicketModal"
        v-sweet-esc
        title="Before you make any changes..."
        @close="clearCommentId"
      >
        <template #default>
          <disable-ticket-overlay
            :creator-name="creatorName"
            @go-to-comment="goToComment"
          />
        </template>
      </sweet-modal>
    </div>
    <show-header
      :is-split-pane-view="isSplitpaneView"
      :is-quick-view="isQuickView && !isSplitpaneView"
      :app-sessions="appSessions"
      :avatar-session="avatarSession"
      :quick-view-ticket-id="ticketId"
      @set-active-component="setActiveComponent"
      @session="setSession"
      @all-sessions="setAllSessions"
      @close-quick-view="$emit('close-quick-view')"
    />
    <div v-if="loadingTicket">
      <help-ticket-detail-skeleton :is-quick-view="isQuickView" />
    </div>
    <div
      v-if="currentHelpTicket && !loadingTicket"
      class="row d-flex-column--medium"
      :class="{'mb-7 pb-4': isQuickView }"
    >
      <div
        v-if="!isQuickView"
        class="arrows-holder position-relative d-lg-block d-none"
      >
        <div
          v-tooltip="!showPreviousTicketBtn ? 'No more Tickets': ''"
          class="position-fixed left-arrow"
          :class="leftPositionClass"
        >
          <router-link
            :to="previousTicketUrl"
            class="text-secondary p-2 not-as-small previous"
            :class="{ 'disable-buttons': !showPreviousTicketBtn }"
            role="button"
            @click.native="onPreviousTicketChange"
          >
            <i
              v-tooltip="!previousTicket ? 'Load More Tickets': ''"
              class="nulodgicon-chevron-left"
            />
            <next-previous-ticket-preview
              v-if="previousTicket"
              :ticket="previousTicket"
              :previous-ticket="true"
            />
          </router-link>
        </div>
        <div
          v-tooltip="!showNextTicketBtn ? 'No more Tickets': ''"
          class="position-fixed right-arrow"
        >
          <router-link
            :to="nextTicketUrl"
            class="text-secondary mr-4 p-2 not-as-small next"
            :class="{ 'disable-buttons': !showNextTicketBtn }"
            role="button"
            @click.native="onNextTicketChange"
          >
            <i
              v-tooltip="!nextTicket ? 'Load More Tickets': ''"
              class="nulodgicon-chevron-right"
            />
            <next-previous-ticket-preview
              v-if="nextTicket"
              :ticket="nextTicket"
              :next-ticket="true"
            />
          </router-link>
        </div>
      </div>
      <div
        class="col-sm-12 h-100"
        :class="{ 'col-lg-8': !isQuickView }"
      >
        <div
          class="mr-sm-0 mt-4 p-0"
          :class="{ 'box': !isQuickView }"
        >
          <div class="box_inner w-100">
            <div
              v-for="(formField, idx) in leftFields"
              :key="`field-${idx}`"
              class="mt-3 mb-3 mr-sm-0 mr-lg-4"
              :class="{ 'ml-4': !isQuickView, 'ml-3': isQuickView }"
            >
              <field-renderer
                :form-field="formField"
                :object="currentHelpTicket"
                class="mb-3"
                :class="{ 'trix-toolbar--small': isDescriptionAndQuickView(formField) }"
                :is-quick-view="isQuickView"
                show-label
                @update-field="updateFormField"
              />
            </div>

            <div
              class="lower-section"
              :class="{'box p-0 mt-5 mb-3': isQuickView}"
            >
              <div
                id="active-component-nav"
                class="mt-sm-3"
                :class="{'w-100': isQuickView, 'mt-md-4': !isQuickView}"
              >
                <div
                  class="sub-menu clearfix"
                  :class="{ 'px-3': !isQuickView, 'pl-3': isQuickView }"
                >
                  <div class="float-left module-sub-tabs">
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'Comments' }"
                      href="#"
                      data-tc-comments
                      @click.stop.prevent="activeComponent = 'Comments'"
                    >
                      Comments
                    </a>
                    <a
                      v-if="!isBasicAccess"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'Tasks'}"
                      href="#"
                      data-tc-tasks
                      @click.stop.prevent="activeComponent = 'Tasks'"
                    >
                      Tasks
                    </a>
                    <a
                      v-if="isWriteAny"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'TimeSpent'}"
                      href="#"
                      data-tc-time-spent
                      @click.stop.prevent="activeComponent = 'TimeSpent'"
                    >
                      Time Spent
                    </a>
                    <a
                      v-if="attachmentFields.length > 0"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'Attachments'}"
                      href="#"
                      data-tc-attachments
                      @click.stop.prevent="activeComponent = 'Attachments'"
                    >
                      Attachments
                    </a>
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'History'}"
                      href="#"
                      data-tc-history
                      @click.stop.prevent="activeComponent = 'History'"
                    >
                      History
                    </a>
                    <a
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'ScheduledComments'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'ScheduledComments'"
                    >
                      Scheduled Comments
                    </a>
                    <a
                      v-if="currentHelpTicket.hasOriginatingEmail"
                      class="sub-menu-item"
                      :class="{'router-link-exact-active': activeComponent == 'OriginatingEmail'}"
                      href="#"
                      @click.stop.prevent="activeComponent = 'OriginatingEmail'"
                    >
                      Originating Email
                    </a>
                  </div>
                </div>
              </div>

              <div
                class="py-4 bg-lighter px-3 rounded-bottom"
                :class="{'box_inner w-100': isQuickView}"
              >
                <component
                  :is="activeComponent"
                  :new-comment-id="activeComponent == 'Comments' ? newCommentId : null"
                  :scheduled-comment-id="activeComponent === 'ScheduledComments' ? scheduledCommentId : null"
                  :quick-view-ticket-id="ticketId"
                  :is-quick-view="isQuickView && !isSplitpaneView"
                  @show-scheduled-comments="activeComponent = 'ScheduledComments'"
                  @go-to-comment="goToComment"
                  @open-modal="openModal"
                  @render-field="renderField"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="col-sm-12 h-100"
        :class="{ 'col-lg-4 pl-5': !isQuickView }"
      >
        <div class="pb-1 px-4 pt-2 position-relative mt-4 bg-lighter rounded border">
          <survey-field
            v-if="showSurveyField"
            :ticket="currentHelpTicket"
          />

          <div
            v-if="mergeParent"
            class="row mt-2"
          >
            <div class="col-auto">
              <i
                class="genuicon-loop-alt3 text-muted"
                style="font-size: 1.5rem"
              />
            </div>
            <div class="col pl-0 text-secondary not-as-small">
              <label class="mb-0 align-sub">
                Merged with:
              </label>
              <div
                class="hoverable align-sub ml-1 font-weight-semi-bold"
                @click="openMergedParentTicket"
              >
                <span>#{{ mergeParent.ticketNumber }}</span>
                <span>{{ mergeParent.subject }}</span>
              </div>
            </div>
          </div>

          <merged-ticket
            :is-quick-view="isQuickView"
            :merged-tickets="mergedTickets"
            class="my-2"
            @unmerge="unmergeTicket"
            @set-quick-view-ticket-id="setQuickViewTicket"
          />

          <ul v-if="currentHelpTicket.slaResponse">
            <li
              v-if="currentHelpTicket.firstResponseTime && timePassed(currentHelpTicket.firstResponseTime)"
              class="mb-3 text-secondary not-as-small"
            >
              <span
                class="custom-legend bullet-color"
                :class="{ 'bullet-green' : currentHelpTicket.firstResponseReceived }"
              />
              First Response due
              {{ showDateTime(currentHelpTicket.firstResponseTime) }}
            </li>
            <li
              v-if="currentHelpTicket.resolutionTime && timePassed(currentHelpTicket.resolutionTime)"
              class="text-secondary not-as-small"
            >
              <span 
                class="custom-legend bullet-color"
                :class="{ 'bullet-green' : currentHelpTicket.ticketResolved }"
              />
              Resolution due
              {{ showDateTime(currentHelpTicket.resolutionTime) }}
            </li>
          </ul>
          <div
            v-for="(formField, idx) in rightFields"
            :key="`field-${idx}`"
          >
            <field-renderer
              :key="defaultKey"
              class="mb-3"
              :form-field="formField"
              :object="currentHelpTicket"
              :active-dropdown="activeDropdown"
              :is-click-allowed="true"
              show-label
              @update-field="updateFormField"
              @dropdown-toggled="setActiveDropdown"
            />
          </div>

          <universal-links
            v-if="form"
            class="mt-4"
            :custom-form="form"
            :universal-links="universalLinks"
            :label-classes="['text-secondary not-as-small']"
            :is-scope-creator="isScopeCreator || isScopeAssign"
            :source-id="currentHelpTicket.id"
            @remove="removeLink"
            @add="addLink"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import _cloneDeep from 'lodash/cloneDeep';
  import Pusher from 'common/pusher';
  import _debounce from 'lodash/debounce';
  import _get from 'lodash/get';
  import _sortBy from 'lodash/sortBy';
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';

  import channelCleanup from "mixins/custom_forms/channel_cleanup";
  import customForms from 'mixins/custom_forms';
  import universalLink from "mixins/universal_link";
  import companyChannel from 'mixins/company_channel';
  import suggestions from 'mixins/automated_tasks/suggestions';
  import inflections from "mixins/inflections";
  import customFormFields from 'mixins/custom_forms/fields';
  import permissionsHelper from "mixins/permissions_helper";
  import sortHelper from 'mixins/sorting_helper';
  import helpTickets from 'mixins/help_ticket';

  import customFormHelper from "mixins/custom_form_helper";
  import dates from 'mixins/dates';
  import http from "common/http";
  import ticketHelper  from 'mixins/ticket_helper';
  import { removeUnusedAttachments } from 'mixins/trix_vue_helper';
  import subscription from '../../../../stores/mixins/subscription';
  import ShowHeader from '../show_header.vue';
  import History from '../history/index.vue';
  import HelpTicketDetailSkeleton from '../../loading_states/help_ticket_detail_skeleton.vue';
  import Comments from '../comments/index.vue';
  import Tasks from '../project_tasks/index.vue';
  import TimeSpent from '../time_spent/index.vue';
  import Attachments from '../attachments/index.vue';
  import NextPreviousTicketPreview from './next_previous_ticket_preview.vue';
  import MergedTicket from './merged_ticket.vue';
  import FieldRenderer from '../../../shared/custom_forms/renderer.vue';
  import UniversalLinks from '../../../shared/universal_link/universal_links.vue';
  import SurveyField from '../../closing_surveys/field.vue';
  import DisableTicketOverlay from '../disable_ticket_overlay.vue';
  import OriginatingEmail from '../originating_email.vue';
  import ScheduledComments from '../comments/scheduled_comments.vue';

  const ADDITIONAL_SORT_COLUMNS = ["ticket_number", "comment_count", "created_at", "total_time_spent", "updated_at", "workspace_name"];

  export default {
    components: {
      Attachments,
      Comments,
      Tasks,
      History,
      TimeSpent,
      FieldRenderer,
      MergedTicket,
      ShowHeader,
      SurveyField,
      UniversalLinks,
      SweetModal,
      DisableTicketOverlay,
      HelpTicketDetailSkeleton,
      NextPreviousTicketPreview,
      OriginatingEmail,
      ScheduledComments,
    },
    mixins: [
      subscription,
      channelCleanup,
      companyChannel,
      customForms,
      customFormFields,
      inflections,
      permissionsHelper,
      sortHelper,
      customFormHelper,
      suggestions,
      universalLink,
      dates,
      ticketHelper,
      removeUnusedAttachments,
      helpTickets,
    ],
    provide: {
      showUniversalLinkBeforeMenu: false,
    },
    props: {
      isQuickView: {
        type: Boolean,
        default: false,
      },
      isSplitpaneView: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        activeComponent: "Comments",
        listenerUp: false,
        newCommentId: null,
        creatorName: '',
        nextTicket: null,
        previousTicket: null,
        previousChange: false,
        nextChange: false,
        activeDropdown: '',
        subscribedChannels: [],
        appSessions: [],
        avatarSession: null,
        defaultKey: false,
        scheduledCommentId: null,
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'currentHelpTicketDraft',
        'enableTicketDrafts',
        'isTicketDraftEmpty',
        'tickets',
        'loadingTicket',
        'showPreviousTicketBtn',
        'showNextTicketBtn',
        'pageCount',
        'page',
        'loadingStatus',
        'shouldUserRedirectToTickets',
        'currentWorkspace',
      ]),
      ...mapGetters('GlobalStore', ['currentCompanyUser', 'isMoveTicketModalOpen']),
      mergedTickets() {
        return this.currentHelpTicket.mergedTickets;
      },
      ticketShowStyles() {
        return `${!this.isQuickView ? 'mt-4 ' : ''}${!this.isQuickView && this.isOnlyBasicRead ? 'mt-7 px-6' : ''}`;
      },
      nextTicketUrl() {
        if (!(this.tickets && this.tickets.length) || !(this.currentHelpTicket && this.currentHelpTicket.id)) {
          return "";
        }
        const index = this.currentTicketIndex;
        if (index < (this.tickets.length - 1)) {
          this.setNextTicket(this.tickets[index + 1]);
        } else {
          this.setNextTicket(null);
        }
        return "";
      },
      previousTicketUrl() {
        if (!(this.tickets && this.tickets.length) || !(this.currentHelpTicket && this.currentHelpTicket.id)) {
          return "";
        }
        const index = this.currentTicketIndex;
        if (index > 0) {
          this.setPreviousTicket(this.tickets[index - 1]);
        } else {
          this.setPreviousTicket(null);
        }
        return "";
      },
      showSurveyField() {
        const collectClosingSurvey = _get(this, 'form.moduleForm.collectClosingSurvey');
        if (!collectClosingSurvey) {
          return false;
        }
        let values = this. getValuesForName(this.currentHelpTicket, 'status');
        if (!values || values.length === 0) {
          return false;
        }
        if (values[0].valueStr !== 'Closed') {
          return false;
        }
        values = this. getValuesForName(this.currentHelpTicket, 'created_by');
        if (!values || values.length === 0) {
          return false;
        }
        if (values[0].valueInt !== this.$currentContributorId) {
          return false;
        }
        return true;
      },
      mergeParent() {
        const parentId = _get(this, 'currentHelpTicket.mergeParent.id');
        if (parentId) {
          return this.currentHelpTicket.mergeParent;
        }
        return null;
      },
      universalLinks() {
        return this.currentHelpTicket.universalLinks;
      },
      form() {
        if (this.currentHelpTicket) {
          return this.currentHelpTicket.customForm;
        }
        return null;
      },
      leftFields() {
        return this.fields('left');
      },
      rightFields() {
        return this.filterSubscriptions(this.fields('right'));
      },
      attachmentFields() {
        return this.getFieldsByType(this.currentHelpTicket, "attachment");
      },
      isScopeCreator() {
        const values = this.getValuesForName(this.currentHelpTicket, 'created_by');
        if (values[0] && values[0].valueInt === this.$currentContributorId) {
          return true;
        };
        return false;
      },
      selectedTicketId() {
        return this.isQuickView ? this.$route.query?.selectedTicket : this.$route.params?.id;
      },
      isScopeAssign() {
        const values = this.getValuesForName(this.currentHelpTicket, 'assigned_to');
        let assigned = false;
        values.forEach(assignedUser => {
          if (assignedUser.valueInt === this.$currentContributorId) {
            assigned = true;
          };
        });
        return assigned;
      },
      currentTicketIndex() {
        return this.tickets.findIndex(x => x.id === this.currentHelpTicket.id);
      },
    },
    beforeMount(){
      this.setLoadingTicket(true);
    },
    mounted() {
      window.addEventListener('beforeunload', this.beforeWindowUnload);
      this.setupPusherListeners();
    },
    updated() {
      if (this.currentHelpTicket) {
        const index = this.currentTicketIndex;
        if (!this.loadingStatus && this.previousChange) {
          this.changeTicket(this.tickets.length - 1);
          this.previousChange = false;
        }
        if (!this.loadingStatus && this.nextChange) {
          this.changeTicket(0);
          this.nextChange = false;
        }
        if (index !== -1) {
          this.checkTicketindex(index);
        }
      }
    },
    beforeDestroy() {
      if (this.enableTicketDrafts) {
        this.$store.dispatch("handleTicketDraft");
      }
      window.removeEventListener('beforeunload', this.beforeWindowUnload);
      if (this.avatarSession || !this.$superAdminUser || this.$currentUserId) {
        this.destroyTicketSession();
      }
      this.unbindAll(this.subscribedChannels);
      this.subscribedChannels = [];
    },
    methods: {
      ...mapMutations([
        'addUniversalLinkToTicket',
        'removeComment',
        'removeUniversalLinkToTicket',
        'setActionSuggestion',
        'setCurrentHelpTicket',
        'setCurrentHelpTicketDraft',
        'setSortColumn',
        'setSortDirection',
        'setSortType',
        'setLoadingTicket',
        'setPreviousBtn',
        'setNextBtn',
        'setPage',
        'setWorkspaceFilter',
        'setShouldUserRedirectToTickets',
        'setQuickViewTicketId',
      ]),
      ...mapActions([
        'createWindowSession',
        'fetchComment',
        'fetchFormValue',
        'fetchCustomEmails',
        'destroyTicketSession',
        'fetchCompanyUserOptions',
      ]),
      ...mapActions('GlobalStore', ['fetchCurrentCompanyUser']),

      onWorkspaceChange() {
        const commentId = this.$route.query.comment_id;
        if (commentId) {
          this.handleScheduledComment(commentId);
        }
        this.loadDraft();
        if (!this.ticketId || this.isMoveTicketModalOpen) {
          return;
        }
        this.setDefaultSort();
        this.fetchCompanyUserOptions({ archived_users_only : true });
        this.setCurrentHelpTicket(null);
        this.refreshTicket().then(() => {
          if (this.tickets.length === 0) {
            this.$store.dispatch('fetchTickets');
          };
          if (!this.currentCompanyUser) {
            this.fetchCurrentCompanyUser({ fetch_permission: false });
          };
          if (this.currentHelpTicket) {
            this.updateTicketSeen();
          };
        });
        this.createWindowSession();

        this.$store.dispatch("fetchAgentCheck");
        this.$store
          .dispatch("fetchTasks", this.ticketId)
          .catch(err => {
            this.emitError(`Sorry, there was an error loading tasks. ${err.response.data.message}`);
          });
        this.setActionSuggestion(null);
        this.fetchCustomEmails();
        if (!this.$superAdminUser) {
          this.setupListeners();
        }
      },
      setupListeners() {
        if (this.$pusher && this.selectedTicketId) {
          const channel = this.$pusher.subscribe(this.selectedTicketId);
          channel.bind(`app-session-deleted`, data => {
            this.removeAppSession(data);
          }, this);
          channel.bind(`app-session-created`, data => {
            this.addAppSession(data);
          }, this);
        }
      },
      handleScheduledComment(commentId) {
        this.activeComponent = 'ScheduledComments';
        this.scheduledCommentId = parseInt(commentId, 10);
      },
      onPreviousTicketChange() {
        const index = this.currentTicketIndex;
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
        if (index === 0 && this.page > 0 && !this.previousChange) {
          this.setPage(this.page - 1);
          this.loadDraft();
          this.$store.dispatch('fetchTickets');
          this.previousChange = true;
        } else if (this.tickets[index - 1]) {
          this.$router.replace(`/${this.tickets[index - 1].id}`);
          this.loadDraft();
          this.loadHelpTicket();
        }
        this.handleEmptyDraft();
      },
      setActiveDropdown(field) {
        this.activeDropdown = field;
      },
      setNextTicket(ticket) {
        this.nextTicket = ticket;
      },
      setPreviousTicket(ticket) {
        this.previousTicket = ticket;
      },
      onNextTicketChange() {
        const index = this.currentTicketIndex;
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
        if (index === (this.tickets.length - 1) && this.page < this.pageCount - 1 && !this.nextChange) {
          this.setPage(this.page + 1);
          this.loadDraft();
          this.$store.dispatch('fetchTickets');
          this.nextChange = true;
        } else if (this.tickets[index + 1]) {
          this.$router.replace(`/${this.tickets[index + 1].id}`);
          this.loadDraft();
          this.loadHelpTicket();
        }
        this.handleEmptyDraft();
      },
      openMergedParentTicket() {
        if (this.isQuickView) {
          this.setQuickViewTicket(this.mergeParent.id);
        } else {
          window.open(`/help_tickets/${this.mergeParent.id}`);
        }
      },
      setQuickViewTicket(ticketId) {
        this.setQuickViewTicketId(ticketId);
        this.loadHelpTicket();
      },
      loadHelpTicket() {
        this.setCurrentHelpTicket(null);
        this.setLoadingTicket(true);
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      loadDraft() {
        this.setCurrentHelpTicketDraft({
          helpTicketId: this.ticketId,
          workspaceId: null,
          companyId: null,
          companyUserId: this.currentCompanyUser?.id || this.$currentCompanyUserId,
          fieldsData: {},
          comments: {},
          timeSpents: {},
          tasksData: {},
        });
        this.$store.dispatch("fetchTicketDraft", this.ticketId);
      },
      beforeWindowUnload() {
        if (this.enableTicketDrafts) {
          this.$store.dispatch("handleTicketDraft");
        }
      },
      checkTicketindex(idx) {
        if ((this.tickets.length) === 1 && this.pageCount === 1) {
          this.setPreviousBtn(false);
          this.setNextBtn(false);
        } else if (idx === 0 && this.page === 0) {
          this.setPreviousBtn(false);
          this.setNextBtn(true);
        } else if (idx === (this.tickets.length - 1) && this.page === this.pageCount - 1) {
          this.setNextBtn(false);
          this.setPreviousBtn(true);
        } else if (idx >= 0 && idx < this.tickets.length) {
          this.setPreviousBtn(true);
          this.setNextBtn(true);
        }
      },
      changeTicket(idx) {
        this.$router.push(`/${this.tickets[idx].id}`);
        this.loadHelpTicket();
      },
      setDefaultSort() {
        this.activeSort = JSON.parse(localStorage.getItem('active_sort'));
        if (this.activeSort && !ADDITIONAL_SORT_COLUMNS.includes(this.activeSort)) {
          this.activeSortType = JSON.parse(localStorage.getItem('active_sort_type'));
        }
        if (!this.activeSort) {
          this.activeSort = 'priority';
          this.activeSortType = 'priority';
        }
        this.activeSortDirection = JSON.parse(localStorage.getItem('active_sort_direction'));
        if (!this.activeSortDirection) this.activeSortDirection = 'desc';

        this.setSortColumn(this.activeSort);
        this.setSortDirection(this.activeSortDirection);
        this.setSortType(this.activeSortType);
      },
      loadTickets() {
        if (this.tickets.length === 0) {
          this.refreshTickets();
        }
      },
      setupPusherListeners() {
        Pusher.then(() => {
          if (this.$pusher && this.currentHelpTicket && !this.listenerUp) {
            this.listenerUp = true;

            const channelId = `help_ticket=${this.currentHelpTicket.guid}`;
            const channel = this.$pusher.subscribe(channelId);
            this.subscribedChannels.push(channel);

            // Originally we were passing the values via pusher, BUT...
            // there is a limit on the size of the message you can send via Pusher (4k)
            // so we have to just pass the id's and get the values here
            channel.bind(`form-field`, data => {
              if (this.shouldUserRedirectToTickets) {
                this.$router.push("/");
                this.setShouldUserRedirectToTickets(false);
                return;
              }

              if (!data || !this.currentHelpTicket ) {
                return;
              }

              this.fetchFormValue(data.id).then(() => this.refreshActivities());
            }, this);
            channel.bind(`ticket-due-time`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.currentHelpTicket.resolutionTime = data.resolution_time;
              this.currentHelpTicket.firstResponseTime = data.first_response_time;
              this.currentHelpTicket.firstResponseReceived = data.first_response_received;
              this.currentHelpTicket.ticketResolved = data.ticket_resolved;
            }, this);

            channel.bind(`comment-added`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              if (this.currentHelpTicket.id === data.helpTicketId) {
                if (data.creator_ids && !data.creator_ids.includes(this.$currentContributorId)) {
                  this.newCommentId = data.id;
                  this.creatorName = data.creator_name;
                  if (this.activeComponent !== 'Comments') {
                    this.$refs.disableTicketModal.open();
                  }
                }
                this.fetchComment(data);
              }
            }, this);

            channel.bind(`comment-deleted`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.removeComment(data);
            }, this);

            channel.bind(`universal-link-added`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.addUniversalLinkToTicket(data);
              this.refreshActivities();
            }, this);

            channel.bind(`universal-link-deleted`, data => {
              if (!data || !this.currentHelpTicket) {
                return;
              }
              this.removeUniversalLinkToTicket(data);
              this.refreshActivities();
            }, this);
          }
        });
      },
      removeAppSession(data) {
        const storage = window.sessionStorage;
        if (data.helpTicketId.toString() !== this.selectedTicketId) {
          return;
        }
        const appSession = this.appSessions.find(s => s.requestSessionId === data.requestSessionId);
        if (appSession && appSession.windowGuid !== storage.getItem("windowGuid")) {
          const idx = this.appSessions.indexOf(appSession);
          if (idx > -1) {
            this.appSessions.splice(idx, 1);
          }
        }
      },
      addAppSession(data) {
        if (data.helpTicketId.toString() !== this.selectedTicketId) {
          return;
        }
        const oldValue = this.appSessions.find(s => s.companyUserId === data.companyUserId && s.helpTicketId === data.helpTicketId);
        if (oldValue && oldValue.sessionId !== data.sessionId) {
          const idx = this.appSessions.indexOf(oldValue);
          this.appSessions.splice(idx, 1);
        } else if (!oldValue) {
          this.appSessions.push(data);
        }
        if (data.sessionId === this.$sessionId) {
          this.avatarSession = data;
        }
      },
      setAllSessions(sessions) {
        this.appSessions = sessions;
      },
      setSession(session) {
        this.avatarSession = session;
      },
      updateTicketSeen() {
        if (this.isBold || !this.currentCompanyUser) {
          const params = {
            help_ticket: this.currentHelpTicket.id,
            company_id: this.currentHelpTicket.company.id,
            user_matched: this.isBold,
          };
          http
            .put(`/tickets/${this.currentHelpTicket.id}/update_ticket_seen_status.json`, params)
            .catch(() => {
              this.emitError('An error occurred while updating ticket seen status.');
            });
        }
      },
      refreshTicket() {
        return this.$store.dispatch("fetchTicket", this.ticketId)
          .then(() => {
            this.setupPusherListeners();
            if (!this.currentWorkspace) {
              const workspace = getWorkspaceFromStorage();
              if (this.currentHelpTicket && workspace && workspace.id !== this.currentHelpTicket.workspaceId) {
                if ($workspaceSelectedFromDropdown) {
                  this.$router.push("/");
                } else {
                  setWorkspaceToStorage(this.currentHelpTicket.workspaceJson);
                  this.setWorkspaceFilter(this.currentHelpTicket.workspaceJson);
                  if (this.workspaceFilter) {
                    const storage = window.sessionStorage;
                    storage.setItem("workspaceFilter", JSON.stringify(this.workspaceFilter));
                  }
                }
              }
            }
          });
      },
      refreshActivities: _debounce(
        function refreshActivitiesFunction() {
          if (this.currentHelpTicket) {
            this.$store.dispatch("fetchActivities", this.currentHelpTicket.id);
          } else if (this.ticketId) {
            this.$store.dispatch("fetchActivities", this.ticketId);
          }
        }, 1000),
      fields(section) {
        if (this.form) {
          const fields = this.form.formFields.filter(field => field.fieldPosition && field.fieldPosition.position === section);
          return _sortBy(fields, 'orderPosition');
        }
        return [];
      },
      addLink(link) {
        if (this.currentHelpTicket.linkableId !== link.linkableId) {
          const params = {
            source_id: this.currentHelpTicket.linkableId,
            target_id: link.id,
            company_id: this.currentHelpTicket.company.id,
          };
          this.addUniversalLink(params);
        } else {
          this.emitError(`Sorry, you cannot link an item with itself.`);
        }
      },
      removeLink(link) {
        if (link && link.id) {
          this.removeUniversalLink(link.id);
        }
      },
      updateFormField(params) {
        this.updateField(params)
          .then((res) => {
            this.setFieldInTicket(res.data);
          });
      },
      setActiveComponent(component) {
        this.activeComponent = component;
        document.querySelector('#active-component-nav').scrollIntoView({behavior: "smooth", block: "center"});
      },
      goToComment() {
        if (this.activeComponent !== 'Comments') {
          this.activeComponent = 'Comments';
          this.$refs.disableTicketModal.close();
        } else {
          const comment = document.querySelector(`[data-comment-id="${this.newCommentId}"]`);
          if (comment) {
            comment.scrollIntoView({behavior: 'smooth', block: 'center'});
            comment.classList.add("highlight-comment");
            setTimeout(() => {
              comment.classList.remove("highlight-comment");
            }, 5000);
          }
          this.$refs.disableTicketModal.close();
          this.newCommentId = null;
        }
      },
      openModal() {
        this.$refs.disableTicketModal.open();
      },
      clearCommentId() {
        this.newCommentId = null;
      },
      unmergeTicket(ticket) {
        const newTicket = _cloneDeep(this.currentHelpTicket);
        const idx = newTicket.mergedTickets.indexOf(ticket);
        if (idx) {
          newTicket.mergedTickets.splice(idx, 1);
          this.setCurrentHelpTicket(newTicket);
        }
      },
      timePassed(time) {
        return Date.parse(time) > new Date();
      },
      isDescriptionAndQuickView(field) {
        return field.name === 'description' && this.isQuickView;
      },
      renderField() {
        this.defaultKey = !this.defaultKey;
      },
      handleEmptyDraft() {
        if (this.isTicketDraftEmpty && this.currentHelpTicketDraft?.id) {
          this.$store.dispatch('deleteTicketDraft', this.currentHelpTicketDraft.id);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
.arrows-holder {
  z-index: 999;
  .left-arrow,
  .right-arrow {
    top: 50vh;
    z-index: 9999;
  }
  .right-arrow {
    right: -2rem;
  }
}

.hoverable {
  color: $themed-muted;
  &:hover {
    color: #000;
    font-weight: bold;
  }
}

.disable-buttons {
  opacity: 0.3;
  pointer-events: none;
}

.nulodgicon-chevron-left,
.nulodgicon-chevron-right {
  font-size: 2.25rem;
}

.next {
  &:hover {
    .next-button-box {
      transform: translate(-72%);
    }
  }
}

.previous {
  &:hover {
    .previous-button-box {
      transform: translate(2%);
    }
  }
}
.bullet-color {
  background-color: #CC3333;
}

.bullet-green {
  background-color: #47e047;
}
.arrows-holder {
  .basic-access-left-arrow {
    left: 0rem;
  }
}
.sidebar-left-open {
  left: 4rem;
}

.sidebar-left-closed {
  left: 0;
}
</style>
