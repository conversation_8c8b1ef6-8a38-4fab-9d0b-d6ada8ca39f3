module CompanyModule
  module CustomForms
    class EntityUpdate
      attr_accessor :params, :current_location, :current_user, :helpdesk_module, :current_company_user, :custom_entity_values

      def initialize(params = nil, current_location = nil, current_user = nil, helpdesk_module = nil, current_company_user = nil)
        self.params = params
        self.custom_entity_values = []
        self.current_user = current_user
        self.helpdesk_module = helpdesk_module
        self.current_location = current_location
        self.current_company_user = current_company_user
      end

      def call
        ActiveRecord::Base.transaction do
          delete_custom_form_values
          update_custom_form_values
          update_mute_notification
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
          true
        rescue => e
          Rails.logger.error("Transaction failed: #{e.message}")
          ActiveRecord::Base.connection.execute "ROLLBACK"
        end

        if helpdesk_module.present?
          custom_entity_values.each do |custom_entity_value|
            AutomatedTasks::EventRouting.perform_async(custom_entity_value[:id], custom_entity_value[:name], custom_entity_value[:before_attributes])
          end
        end
      end

      def update_mute_notification
        json_params = JSON.parse(params["params"]["json"])
        if json_params.key?("muteNotification")
          help_ticket = HelpTicket.find_by(id: params["id"])
          if help_ticket.present?
            mute_notification = json_params["muteNotification"]
            data = { current_value: mute_notification ? 'Mute' : 'Unmute', activity_label: 'notification' }
            help_ticket.update_columns(mute_notification: mute_notification)
            ::HelpTickets::Ticket.new(help_ticket, current_company_user, nil, data).create_activity
          end
        end
      end

      def delete_custom_form_values
        return unless params["params"]["json"]["deletedValues"].present?
        deleted_values = JSON.parse(params["params"]["json"])["deletedValues"]

        entity_form_fields = CustomFormField.where(id: deleted_values.pluck("customFormFieldId")).index_by(&:id)
        entity_form_values = custom_form.custom_form_values.where(
          module_id: params['id'],
          module_type: module_type,
          custom_form_field_id: entity_form_fields.keys
        )

        entity_form_values.each do |entity_form_value|
          entity_form_field = entity_form_fields[entity_form_value.custom_form_field_id]
          if entity_form_field.field_attribute_type.in?(%w[tag checkbox list])
            value_str = entity_form_value.value_str
          else
            value_int = entity_form_value.value_int
          end
          deleted_value = deleted_values.find { |dv| 
            dv['customFormFieldId'] == entity_form_value.custom_form_field_id &&
            (value_str.present? ? dv['value'] == value_str : dv['value'] == value_int)
          }
          cf_value = deleted_value['value'] if deleted_value.present?
      
          if entity_form_field.present? && cf_value.present?  
            if helpdesk_module.present?
              create_help_ticket_activity(entity_form_value, cf_value, nil)
            elsif module_type == "CompanyUser"
              create_user_activity(entity_form_value, cf_value, nil)
            end
    
            entity_form_value.destroy
          end
        end
      end

      def update_custom_form_values
        entity_form_fields = CustomFormField.where(id: custom_form_values.pluck("customFormFieldId")).index_by(&:id)
        entity_form_attachments = CustomFormAttachment.where(id: custom_form_values.pluck("attachmentId")).index_by(&:id)

        custom_form_values.each do |custom_form_value|
          custom_form_field = entity_form_fields[custom_form_value["customFormFieldId"]]

          if custom_form_field.singular?
            if custom_form_value["valueStr"] || custom_form_value["valueInt"] || custom_form_value["attachmentId"]
              cfv = custom_form.custom_form_values.find_or_initialize_by(
                module_id: params["id"],
                module_type: module_type,
                id: custom_form_value["customFormValueId"],
                custom_form_field_id: custom_form_field.id
              )

              p_attributes = cfv.attributes
              cfv.value_str = custom_form_value["valueStr"] unless custom_form_value["valueStr"].nil?
              cfv.value_int = custom_form_value["valueInt"] unless custom_form_value["valueInt"].nil?

              if cfv.save!
                if helpdesk_module.present? && p_attributes != cfv.attributes
                  before_attributes = p_attributes || {}
                  custom_entity_values << {
                    id: cfv.id,
                    name: cfv.class.name,
                    before_attributes: before_attributes.as_json,
                  }
                end
              end

              set_activity_data(cfv) if helpdesk_module.present? || module_type == "CompanyUser"

              attachment = entity_form_attachments[custom_form_value["attachmentId"]]
              if attachment.present?
                CustomFormAttachment.where(custom_form_value_id: cfv.id).where.not(id: custom_form_value["attachmentId"]).destroy_all
                attachment.update_attribute('custom_form_value_id', cfv.id)
              end
            end
          else
            if ['tag', 'checkbox'].include?(custom_form_field.field_attribute_type)
              cfv = custom_form.custom_form_values.find_or_initialize_by(
                module_id: params["id"],
                module_type: module_type,
                value_str: custom_form_value["valueStr"],
                custom_form_field_id: custom_form_field.id
              )
            else
              if custom_form_value["valueStr"].present? && custom_form_field&.people_list?
                found_user = User.find_by_cache(email: custom_form_value["valueStr"].strip.downcase)

                if found_user
                  found_company_user = CompanyUser.find_by_cache(user_id: found_user.id, company_id: current_company_user.company_id)
                  if found_company_user
                    custom_form_value["valueInt"] = found_company_user.contributor_id
                    custom_form_value["valueStr"] = nil
                  end
                end
              end

              value_int = custom_form_values.select { |cfv| cfv['customFormFieldId'] == custom_form_field&.id }.pluck('valueInt')
              custom_form.custom_form_values.where(
                module_id: params["id"],
                module_type: module_type,
                custom_form_field_id: custom_form_field.id
              ).where.not(value_int: value_int)&.destroy_all

              cfv = custom_form.custom_form_values.find_or_initialize_by(
                module_id: params["id"],
                module_type: module_type,
                value_int: custom_form_value["valueInt"],
                value_str: custom_form_value["valueStr"],
                custom_form_field_id: custom_form_field.id
              )
            end
            p_attributes = cfv.attributes

            if cfv.save!
              if helpdesk_module.present? && p_attributes != cfv.attributes
                custom_entity_values << {
                  id: cfv.id,
                  name: cfv.class.name,
                  before_attributes: {}.as_json,
                }
              end
            end

            set_activity_data(cfv) if helpdesk_module.present? || module_type == "CompanyUser"
          end
        end
      end

      def custom_form
        @form ||= CustomForm.find(params["custom_form_id"])
      end

      def module_type
        @module_type ||= custom_form.company_module == "helpdesk" ? 'HelpTicket' : custom_form.company_module.camelize
      end

      def custom_form_values
        @custom_form_values ||= begin
          user_fields_ids = []
          user_fields_ids = custom_form.custom_form_fields.where(name: %w[first_name last_name email]).ids if custom_form.company_module == "company_user"
          if params["params"].present?
            JSON.parse(params["params"]["json"])["values"].reject { |val| user_fields_ids.include?(val['customFormFieldId']) }
          else
            params['values'].as_json.map { |val| val.deep_transform_keys! { |key| key.camelize(:lower) }}
          end
        end
      end

      def set_activity_data(cfv)
        current_form_values = ['created_at', 'updated_at', 'custom_form_field_id', 'created_at', 'updated_at', 'custom_form_id', 'company_id', 'module_type', 'module_id', 'id']
        changes = cfv.saved_changes.except(*current_form_values)

        return unless changes.present?
        key = changes.keys.first
        previous_value = changes[key.to_sym][0]
        current_value = changes[key.to_sym][1]
        if current_value.present? || previous_value.present?
          create_help_ticket_activity(cfv, previous_value, current_value) if helpdesk_module.present?
          create_user_activity(cfv, previous_value, current_value) if module_type == "CompanyUser"
        end
      end

      def create_help_ticket_activity(cfv, previous_value, current_value)
        ticket = HelpTicket.find_by(id: params['id'])
        custom_form_field = cfv.custom_form_field

        HelpTicketActivity.create(
          help_ticket: ticket,
          activity_action: 'ticket_update',
          owner_id: current_company_user.id,
          activity_type: custom_form_field.field_attribute_type,
          data: {
            current_value: current_value,
            previous_value: previous_value,
            ticket_subject: ticket.subject,
            ticket_number: ticket.ticket_number,
            activity_label: custom_form_field.label
          }
        )
      end

      def create_user_activity(cfv, previous_value, current_value)
        if current_value.present? && previous_value.present?
          activity_type = CompanyUserActivity.activity_types["updated"]
        elsif current_value.present?
          activity_type = CompanyUserActivity.activity_types["added"]
        else
          activity_type = CompanyUserActivity.activity_types["removed"]
        end

        activity_params = {
          activity_type: activity_type,
          owner_id: current_company_user&.id,
          data: {
            activity_label: cfv.custom_form_field.label,
            field_type: cfv.custom_form_field.field_attribute_type,
            current_value: current_value,
            previous_value: previous_value
          }
        }
        cfv.module.activities.create(activity_params)
      end
    end
  end
end
